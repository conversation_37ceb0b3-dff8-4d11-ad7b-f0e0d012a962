'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatDate } from '@/lib/utils'
import { 
  Search, Phone, Mail, Calendar, UserX, MessageSquare, 
  Clock, ArrowLeft, Loader2, CheckCircle, AlertCircle 
} from 'lucide-react'
import Link from 'next/link'

interface DroppedStudent {
  id: string
  user: {
    id: string
    name: string
    phone: string
    email?: string
  }
  level: string
  branch: string
  droppedAt: string
  reEnrollmentNotes?: string
  lastContactedAt?: string
  enrollments: {
    id: string
    group: {
      name: string
      course: {
        name: string
        level: string
      }
    }
  }[]
}

export default function DroppedStudentsPage() {
  const [droppedStudents, setDroppedStudents] = useState<DroppedStudent[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStudent, setSelectedStudent] = useState<DroppedStudent | null>(null)
  const [isContactDialogOpen, setIsContactDialogOpen] = useState(false)
  const [contactNotes, setContactNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchDroppedStudents()
  }, [])

  const fetchDroppedStudents = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/students/dropped')
      const data = await response.json()
      setDroppedStudents(data.droppedStudents || [])
      setError(null)
    } catch (error) {
      console.error('Error fetching dropped students:', error)
      setError('Failed to fetch dropped students')
    } finally {
      setLoading(false)
    }
  }

  const filteredStudents = droppedStudents.filter(student =>
    student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.user.phone.includes(searchTerm) ||
    student.user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleContactStudent = async () => {
    if (!selectedStudent) return

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch(`/api/students/dropped?studentId=${selectedStudent.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reEnrollmentNotes: contactNotes,
          lastContactedAt: new Date().toISOString(),
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update contact information')
      }

      setSuccess('Contact information updated successfully')
      setIsContactDialogOpen(false)
      setContactNotes('')
      setSelectedStudent(null)
      fetchDroppedStudents() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleReEnroll = async (studentId: string) => {
    if (!confirm('Are you sure you want to re-enroll this student? This will change their status to ACTIVE.')) {
      return
    }

    try {
      const response = await fetch(`/api/students/${studentId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: 'ACTIVE',
          reason: 'Re-enrolled from dropped status'
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to re-enroll student')
      }

      setSuccess('Student re-enrolled successfully')
      fetchDroppedStudents() // Refresh the list
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    }
  }

  const getDaysSinceDropped = (droppedAt: string) => {
    const dropped = new Date(droppedAt)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - dropped.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getDaysSinceContact = (lastContactedAt?: string) => {
    if (!lastContactedAt) return null
    const contacted = new Date(lastContactedAt)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - contacted.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dropped students...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center space-x-2">
            <Link href="/dashboard/students">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Students
              </Button>
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mt-4">Dropped Students Management</h1>
          <p className="text-gray-600">Contact and re-enroll dropped students - Lead-like functionality</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Dropped</CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{droppedStudents.length}</div>
            <p className="text-xs text-muted-foreground">
              Available for re-enrollment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recently Contacted</CardTitle>
            <MessageSquare className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {droppedStudents.filter(s => s.lastContactedAt && getDaysSinceContact(s.lastContactedAt)! <= 7).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Contacted in last 7 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Need Follow-up</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {droppedStudents.filter(s => !s.lastContactedAt || getDaysSinceContact(s.lastContactedAt)! > 14).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Not contacted in 14+ days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search Dropped Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, phone, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Dropped Students Table */}
      <Card>
        <CardHeader>
          <CardTitle>Dropped Students ({filteredStudents.length})</CardTitle>
          <CardDescription>
            Students who have dropped and are available for re-enrollment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Last Course</TableHead>
                <TableHead>Dropped</TableHead>
                <TableHead>Last Contact</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => {
                const daysSinceDropped = getDaysSinceDropped(student.droppedAt)
                const daysSinceContact = getDaysSinceContact(student.lastContactedAt)
                const lastEnrollment = student.enrollments[0] // Most recent enrollment

                return (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                            <UserX className="h-5 w-5 text-red-600" />
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{student.user.name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {student.user.phone}
                          </div>
                          {student.user.email && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {student.user.email}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {lastEnrollment ? (
                        <div>
                          <div className="font-medium">{lastEnrollment.group.course.name}</div>
                          <div className="text-sm text-gray-500">Level: {lastEnrollment.group.course.level}</div>
                          <div className="text-sm text-gray-500">Group: {lastEnrollment.group.name}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">No previous enrollment</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{formatDate(new Date(student.droppedAt))}</div>
                        <div className="text-gray-500">{daysSinceDropped} days ago</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {student.lastContactedAt ? (
                        <div className="text-sm">
                          <div>{formatDate(new Date(student.lastContactedAt))}</div>
                          <div className="text-gray-500">{daysSinceContact} days ago</div>
                        </div>
                      ) : (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                          Never contacted
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {student.reEnrollmentNotes ? (
                          <p className="text-sm text-gray-600 truncate" title={student.reEnrollmentNotes}>
                            {student.reEnrollmentNotes}
                          </p>
                        ) : (
                          <span className="text-gray-400 text-sm">No notes</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                setSelectedStudent(student)
                                setContactNotes(student.reEnrollmentNotes || '')
                                setIsContactDialogOpen(true)
                              }}
                            >
                              <MessageSquare className="h-4 w-4 mr-1" />
                              Contact
                            </Button>
                          </DialogTrigger>
                        </Dialog>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReEnroll(student.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          Re-enroll
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
          
          {filteredStudents.length === 0 && (
            <div className="text-center py-12">
              <UserX className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No dropped students found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contact Dialog */}
      <Dialog open={isContactDialogOpen} onOpenChange={setIsContactDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Contact Student</DialogTitle>
            <DialogDescription>
              Record contact attempt and notes for {selectedStudent?.user.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Contact Notes</label>
              <Textarea
                placeholder="Enter notes about the contact attempt, student interest, next steps, etc."
                value={contactNotes}
                onChange={(e) => setContactNotes(e.target.value)}
                rows={4}
                className="mt-1"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsContactDialogOpen(false)
                  setContactNotes('')
                  setSelectedStudent(null)
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleContactStudent}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Contact'
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
